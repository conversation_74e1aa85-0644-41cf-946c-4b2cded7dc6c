-- 添加月末预测字段到所有月数据表
-- 用于支持月末预测隔离逻辑

-- 采矿场月数据表
ALTER TABLE cw_ckc_month ADD COLUMN month_end_forecast DECIMAL(18,6) COMMENT '月末预测值';

-- 大山厂月数据表  
ALTER TABLE cw_ds_month ADD COLUMN month_end_forecast DECIMAL(18,6) COMMENT '月末预测值';

-- 泗选厂月数据表
ALTER TABLE cw_sx_month ADD COLUMN month_end_forecast DECIMAL(18,6) COMMENT '月末预测值';

-- 精尾厂月数据表
ALTER TABLE cw_jw_month ADD COLUMN month_end_forecast DECIMAL(18,6) COMMENT '月末预测值';

-- 添加索引以提高查询性能
CREATE INDEX idx_ckc_month_end_forecast ON cw_ckc_month(month_end_forecast);
CREATE INDEX idx_ds_month_end_forecast ON cw_ds_month(month_end_forecast);
CREATE INDEX idx_sx_month_end_forecast ON cw_sx_month(month_end_forecast);
CREATE INDEX idx_jw_month_end_forecast ON cw_jw_month(month_end_forecast);
