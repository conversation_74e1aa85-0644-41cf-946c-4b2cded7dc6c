<template>
  <!-- 价量分析页面 -->
  <div class="cw-jlfx-page">
    <a-card :bordered="false">
      <template #title>
        金属利润影响分析 - 瀑布图
      </template>
      <!-- 查询条件 -->
      <a-space style="margin-bottom: 12px;" align="center">
        <a-radio-group v-model:value="dimension" @change="fetchBarData">
          <a-radio-button value="month">月</a-radio-button>
          <a-radio-button value="year">年</a-radio-button>
        </a-radio-group>
        <a-date-picker
          v-model:value="barDate"
          :picker="pickerType"
          :allowClear="false"
          @change="fetchBarData"
        />
      </a-space>

      <!-- 影响概览卡片 -->
      <div class="impact-summary">
        <!-- 价格影响列表 -->
        <div class="impact-item price">
          <div class="label">价格影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="p in priceImpactList" :key="p.metal">
              <span class="metal">{{ p.metal }}</span>
              <span :class="p.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(p.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalPriceImpact) }}</div>
        </div>
        <!-- 产量影响列表 -->
        <div class="impact-item volume">
          <div class="label">产量影响 (万元)</div>
          <div class="impact-list">
            <div class="impact-row" v-for="v in volumeImpactList" :key="v.metal">
              <span class="metal">{{ v.metal }}</span>
              <span :class="v.value >= 0 ? 'pos' : 'neg'">{{ formatNumber(v.value) }}</span>
            </div>
          </div>
          <a-divider style="margin:6px 0" />
          <div class="total">合计：{{ formatNumber(totalVolumeImpact) }}</div>
        </div>
      </div>

      <!-- 瀑布图容器 -->
      <div class="waterfall-charts">
        <div class="chart-container">
          <h3 class="chart-title">价格影响瀑布图</h3>
          <div ref="priceWaterfallRef" class="waterfall-chart"></div>
        </div>
        <div class="chart-container">
          <h3 class="chart-title">产量影响瀑布图</h3>
          <div ref="volumeWaterfallRef" class="waterfall-chart"></div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup name="cw-price-volume-statistics">
  // 价量分析脚本
  import { ref, onMounted, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { message } from 'ant-design-vue';

  // 接口
  import { metalProfitBar } from '/@/api/cw/statistics';
  // 工具
  import { useECharts } from '/@/hooks/web/useECharts';
  import { formatNumber } from '/@/utils/showUtils';

  /** 查询条件 */
  const dimension = ref<'month' | 'year'>('month');
  const barDate = ref(dayjs());
  const pickerType = computed(() => dimension.value);

  /** 瀑布图实例 */
  const priceWaterfallRef = ref<HTMLDivElement | null>(null);
  const volumeWaterfallRef = ref<HTMLDivElement | null>(null);
  // @ts-ignore
  const { setOptions: setPriceWaterfallOptions } = useECharts(priceWaterfallRef as any);
  // @ts-ignore
  const { setOptions: setVolumeWaterfallOptions } = useECharts(volumeWaterfallRef as any);

  /** 数据源 */
  const barList = ref<any[]>([]);

  /** 影响汇总 */
  const totalPriceImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.priceImpact ?? 0), 0);
  });
  const totalVolumeImpact = computed(() => {
    return barList.value.reduce((sum, item) => sum + Number(item.volumeImpact ?? 0), 0);
  });

  /** 分金属影响列表 */
  const priceImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.priceImpact ?? 0) }));
  });
  const volumeImpactList = computed(() => {
    return barList.value.map((item: any) => ({ metal: item.metal, value: Number(item.volumeImpact ?? 0) }));
  });

  /** 监听变化自动刷新 */
  watch([dimension, barDate], () => fetchBarData());

  onMounted(() => {
    fetchBarData();
  });

  /** 获取数据 */
  async function fetchBarData() {
    try {
      const dateStr = barDate.value.format('YYYY-MM-DD');
      barList.value = await metalProfitBar({ date: dateStr, dimension: dimension.value });
      updateWaterfallCharts();
    } catch (e) {
      console.error(e);
      message.error('金属利润数据获取失败');
    }
  }

  /** 更新瀑布图 */
  function updateWaterfallCharts() {
    if (!barList.value?.length) return;

    updatePriceWaterfallChart();
    updateVolumeWaterfallChart();
  }

  /** 更新价格影响瀑布图 */
  function updatePriceWaterfallChart() {
    if (!barList.value?.length) return;

    // 构建瀑布图数据
    const priceData = priceImpactList.value;
    const categories = ['初始值', ...priceData.map(p => p.metal), '最终值'];

    // 计算累积值
    let cumulative = 0;
    const waterfallData = [];

    // 初始值（假设为0）
    waterfallData.push({
      name: '初始值',
      value: 0,
      itemStyle: { color: '#91caff' }
    });

    // 各金属价格影响
    priceData.forEach((item, index) => {
      const value = item.value;
      waterfallData.push({
        name: item.metal,
        value: value,
        itemStyle: {
          color: value >= 0 ? '#ff7875' : '#95de64'
        }
      });
      cumulative += value;
    });

    // 最终值
    waterfallData.push({
      name: '最终值',
      value: cumulative,
      itemStyle: { color: '#1890ff' }
    });

    // 构建堆叠数据用于瀑布效果
    const baseData = [];
    const positiveData = [];
    const negativeData = [];

    let runningTotal = 0;

    waterfallData.forEach((item, index) => {
      if (index === 0) {
        // 初始值
        baseData.push(0);
        positiveData.push(item.value);
        negativeData.push(0);
      } else if (index === waterfallData.length - 1) {
        // 最终值
        baseData.push(0);
        positiveData.push(runningTotal);
        negativeData.push(0);
      } else {
        // 中间值
        if (item.value >= 0) {
          baseData.push(runningTotal);
          positiveData.push(item.value);
          negativeData.push(0);
          runningTotal += item.value;
        } else {
          baseData.push(runningTotal + item.value);
          positiveData.push(0);
          negativeData.push(-item.value);
          runningTotal += item.value;
        }
      }
    });

    setPriceWaterfallOptions({
      title: {
        text: '价格影响瀑布图',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const item = waterfallData[dataIndex];
          if (dataIndex === 0) {
            return `${item.name}: ${formatNumber(item.value)}万元`;
          } else if (dataIndex === waterfallData.length - 1) {
            return `${item.name}: ${formatNumber(item.value)}万元`;
          } else {
            return `${item.name}: ${item.value >= 0 ? '+' : ''}${formatNumber(item.value)}万元`;
          }
        }
      },
      grid: { left: 60, right: 40, bottom: 80, top: 60, containLabel: true },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: (v: any) => formatNumber(v) }
      },
      series: [
        {
          name: '辅助',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: 'transparent' },
          data: baseData
        },
        {
          name: '正向影响',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: '#ff7875' },
          data: positiveData
        },
        {
          name: '负向影响',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: '#95de64' },
          data: negativeData
        }
      ]
    } as any);
  }

  /** 更新产量影响瀑布图 */
  function updateVolumeWaterfallChart() {
    if (!barList.value?.length) return;

    // 构建瀑布图数据
    const volumeData = volumeImpactList.value;
    const categories = ['初始值', ...volumeData.map(v => v.metal), '最终值'];

    // 计算累积值
    let cumulative = 0;
    const waterfallData = [];

    // 初始值（假设为0）
    waterfallData.push({
      name: '初始值',
      value: 0,
      itemStyle: { color: '#91caff' }
    });

    // 各金属产量影响
    volumeData.forEach((item, index) => {
      const value = item.value;
      waterfallData.push({
        name: item.metal,
        value: value,
        itemStyle: {
          color: value >= 0 ? '#ff7875' : '#95de64'
        }
      });
      cumulative += value;
    });

    // 最终值
    waterfallData.push({
      name: '最终值',
      value: cumulative,
      itemStyle: { color: '#1890ff' }
    });

    // 构建堆叠数据用于瀑布效果
    const baseData = [];
    const positiveData = [];
    const negativeData = [];

    let runningTotal = 0;

    waterfallData.forEach((item, index) => {
      if (index === 0) {
        // 初始值
        baseData.push(0);
        positiveData.push(item.value);
        negativeData.push(0);
      } else if (index === waterfallData.length - 1) {
        // 最终值
        baseData.push(0);
        positiveData.push(runningTotal);
        negativeData.push(0);
      } else {
        // 中间值
        if (item.value >= 0) {
          baseData.push(runningTotal);
          positiveData.push(item.value);
          negativeData.push(0);
          runningTotal += item.value;
        } else {
          baseData.push(runningTotal + item.value);
          positiveData.push(0);
          negativeData.push(-item.value);
          runningTotal += item.value;
        }
      }
    });

    setVolumeWaterfallOptions({
      title: {
        text: '产量影响瀑布图',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const item = waterfallData[dataIndex];
          if (dataIndex === 0) {
            return `${item.name}: ${formatNumber(item.value)}万元`;
          } else if (dataIndex === waterfallData.length - 1) {
            return `${item.name}: ${formatNumber(item.value)}万元`;
          } else {
            return `${item.name}: ${item.value >= 0 ? '+' : ''}${formatNumber(item.value)}万元`;
          }
        }
      },
      grid: { left: 60, right: 40, bottom: 80, top: 60, containLabel: true },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          rotate: 45,
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: { formatter: (v: any) => formatNumber(v) }
      },
      series: [
        {
          name: '辅助',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: 'transparent' },
          data: baseData
        },
        {
          name: '正向影响',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: '#ff7875' },
          data: positiveData
        },
        {
          name: '负向影响',
          type: 'bar',
          stack: 'total',
          itemStyle: { color: '#95de64' },
          data: negativeData
        }
      ]
    } as any);
  }
</script>

<style scoped lang="less">
.cw-jlfx-page {
  .ant-card {
    margin-bottom: 16px;

    /* ==== 瀑布图容器样式 ==== */
    .waterfall-charts {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }

    .chart-container {
      background: #fafafa;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .chart-title {
      text-align: center;
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .waterfall-chart {
      width: 100%;
      height: 400px;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .waterfall-charts {
        grid-template-columns: 1fr;
      }

      .waterfall-chart {
        height: 350px;
      }
    }

    /* ==== Impact Cards Modern Style ==== */
    .impact-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin: 12px 0;
    }

    .impact-item {
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 16px 20px;
      display: flex;
      flex-direction: column;
    }

    .impact-item .label {
      font-size: 16px;
      font-weight: 600;
      color: #595959;
      margin-bottom: 8px;
    }

    .impact-list {
      flex: 1;
      border-top: 1px dashed #f0f0f0;
      padding-top: 8px;
      margin-top: 8px;
    }

    .impact-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 4px; /* 增加左右间隙 */
      font-size: 14px;
    }

    .impact-row:not(:last-child) {
      border-bottom: 1px dashed #f0f0f0;
    }

    .impact-row .metal {
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .impact-row .metal::before {
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: #1890ff;
    }

    .impact-row .pos {
      color: #f5222d;
    }
    .impact-row .neg {
      color: #52c41a;
    }

    .total {
      margin-top: 10px;
      font-weight: 600;
      text-align: right;
      font-size: 18px; /* 更突出 */
    }
  }
}
</style>
