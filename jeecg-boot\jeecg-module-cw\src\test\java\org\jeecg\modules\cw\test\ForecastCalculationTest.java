package org.jeecg.modules.cw.test;

import cn.hutool.core.date.DateUtil;
import org.jeecg.modules.cw.common.ForecastChangeDetector;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预测计算逻辑测试类
 * 验证月末预测隔离逻辑和重算逻辑的正确性
 */
public class ForecastCalculationTest {

    @Test
    @DisplayName("测试预测数据变更检测 - 月末预测变更")
    public void testMonthEndForecastChangeDetection() {
        // 测试数据
        BigDecimal oldYys = new BigDecimal("1000");
        BigDecimal newYys = new BigDecimal("1000"); // 月预算未变更
        BigDecimal oldFirstHalf = new BigDecimal("500");
        BigDecimal newFirstHalf = new BigDecimal("500"); // 上半月预测未变更
        BigDecimal oldSecondHalf = new BigDecimal("600");
        BigDecimal newSecondHalf = new BigDecimal("600"); // 下半月预测未变更
        BigDecimal oldMonthEnd = new BigDecimal("700");
        BigDecimal newMonthEnd = new BigDecimal("800"); // 月末预测变更
        
        // 执行检测
        ForecastChangeDetector.ForecastChangeResult result = 
            ForecastChangeDetector.detectChanges(
                oldYys, newYys,
                oldFirstHalf, newFirstHalf,
                oldSecondHalf, newSecondHalf,
                oldMonthEnd, newMonthEnd
            );
        
        // 验证结果
        assertFalse(result.isMonthlyBudgetChanged(), "月预算应该未变更");
        assertFalse(result.isFirstHalfForecastChanged(), "上半月预测应该未变更");
        assertFalse(result.isSecondHalfForecastChanged(), "下半月预测应该未变更");
        assertTrue(result.isMonthEndForecastChanged(), "月末预测应该已变更");
        
        assertFalse(result.needsFullMonthRecalculation(), "不应该需要整月重算");
        assertTrue(result.needsOnlyLastDayRecalculation(), "应该只需要重算月末最后一天");
        assertTrue(result.hasAnyChange(), "应该检测到变更");
    }

    @Test
    @DisplayName("测试预测数据变更检测 - 下半月预测变更")
    public void testSecondHalfForecastChangeDetection() {
        // 测试数据
        BigDecimal oldYys = new BigDecimal("1000");
        BigDecimal newYys = new BigDecimal("1000"); // 月预算未变更
        BigDecimal oldFirstHalf = new BigDecimal("500");
        BigDecimal newFirstHalf = new BigDecimal("500"); // 上半月预测未变更
        BigDecimal oldSecondHalf = new BigDecimal("600");
        BigDecimal newSecondHalf = new BigDecimal("700"); // 下半月预测变更
        BigDecimal oldMonthEnd = new BigDecimal("800");
        BigDecimal newMonthEnd = new BigDecimal("800"); // 月末预测未变更
        
        // 执行检测
        ForecastChangeDetector.ForecastChangeResult result = 
            ForecastChangeDetector.detectChanges(
                oldYys, newYys,
                oldFirstHalf, newFirstHalf,
                oldSecondHalf, newSecondHalf,
                oldMonthEnd, newMonthEnd
            );
        
        // 验证结果
        assertFalse(result.isMonthlyBudgetChanged(), "月预算应该未变更");
        assertFalse(result.isFirstHalfForecastChanged(), "上半月预测应该未变更");
        assertTrue(result.isSecondHalfForecastChanged(), "下半月预测应该已变更");
        assertFalse(result.isMonthEndForecastChanged(), "月末预测应该未变更");
        
        assertTrue(result.needsFullMonthRecalculation(), "应该需要整月重算");
        assertFalse(result.needsOnlyLastDayRecalculation(), "不应该只重算月末最后一天");
        assertTrue(result.hasAnyChange(), "应该检测到变更");
    }

    @Test
    @DisplayName("测试预测数据变更检测 - 多项变更")
    public void testMultipleForecastChanges() {
        // 测试数据 - 同时变更下半月预测和月末预测
        BigDecimal oldYys = new BigDecimal("1000");
        BigDecimal newYys = new BigDecimal("1000"); // 月预算未变更
        BigDecimal oldFirstHalf = new BigDecimal("500");
        BigDecimal newFirstHalf = new BigDecimal("500"); // 上半月预测未变更
        BigDecimal oldSecondHalf = new BigDecimal("600");
        BigDecimal newSecondHalf = new BigDecimal("700"); // 下半月预测变更
        BigDecimal oldMonthEnd = new BigDecimal("800");
        BigDecimal newMonthEnd = new BigDecimal("900"); // 月末预测也变更
        
        // 执行检测
        ForecastChangeDetector.ForecastChangeResult result = 
            ForecastChangeDetector.detectChanges(
                oldYys, newYys,
                oldFirstHalf, newFirstHalf,
                oldSecondHalf, newSecondHalf,
                oldMonthEnd, newMonthEnd
            );
        
        // 验证结果
        assertFalse(result.isMonthlyBudgetChanged(), "月预算应该未变更");
        assertFalse(result.isFirstHalfForecastChanged(), "上半月预测应该未变更");
        assertTrue(result.isSecondHalfForecastChanged(), "下半月预测应该已变更");
        assertTrue(result.isMonthEndForecastChanged(), "月末预测应该已变更");
        
        // 当下半月预测变更时，应该优先整月重算，而不是只重算月末
        assertTrue(result.needsFullMonthRecalculation(), "应该需要整月重算");
        assertFalse(result.needsOnlyLastDayRecalculation(), "不应该只重算月末最后一天");
        assertTrue(result.hasAnyChange(), "应该检测到变更");
    }

    @Test
    @DisplayName("测试预测数据变更检测 - 无变更")
    public void testNoForecastChanges() {
        // 测试数据 - 所有预测数据都未变更
        BigDecimal oldYys = new BigDecimal("1000");
        BigDecimal newYys = new BigDecimal("1000");
        BigDecimal oldFirstHalf = new BigDecimal("500");
        BigDecimal newFirstHalf = new BigDecimal("500");
        BigDecimal oldSecondHalf = new BigDecimal("600");
        BigDecimal newSecondHalf = new BigDecimal("600");
        BigDecimal oldMonthEnd = new BigDecimal("800");
        BigDecimal newMonthEnd = new BigDecimal("800");
        
        // 执行检测
        ForecastChangeDetector.ForecastChangeResult result = 
            ForecastChangeDetector.detectChanges(
                oldYys, newYys,
                oldFirstHalf, newFirstHalf,
                oldSecondHalf, newSecondHalf,
                oldMonthEnd, newMonthEnd
            );
        
        // 验证结果
        assertFalse(result.isMonthlyBudgetChanged(), "月预算应该未变更");
        assertFalse(result.isFirstHalfForecastChanged(), "上半月预测应该未变更");
        assertFalse(result.isSecondHalfForecastChanged(), "下半月预测应该未变更");
        assertFalse(result.isMonthEndForecastChanged(), "月末预测应该未变更");
        
        assertFalse(result.needsFullMonthRecalculation(), "不应该需要整月重算");
        assertFalse(result.needsOnlyLastDayRecalculation(), "不应该只重算月末最后一天");
        assertFalse(result.hasAnyChange(), "不应该检测到任何变更");
    }

    @Test
    @DisplayName("测试月末日期检测")
    public void testLastDayOfMonthDetection() {
        // 测试2024年2月（闰年）
        Date feb28_2024 = DateUtil.parse("2024-02-28", "yyyy-MM-dd");
        Date feb29_2024 = DateUtil.parse("2024-02-29", "yyyy-MM-dd");
        Date mar01_2024 = DateUtil.parse("2024-03-01", "yyyy-MM-dd");
        
        Date endOfFeb2024 = DateUtil.endOfMonth(feb28_2024);
        
        assertFalse(DateUtil.isSameDay(feb28_2024, endOfFeb2024), "2024年2月28日不应该是月末");
        assertTrue(DateUtil.isSameDay(feb29_2024, endOfFeb2024), "2024年2月29日应该是月末");
        assertFalse(DateUtil.isSameDay(mar01_2024, endOfFeb2024), "2024年3月1日不应该是2月月末");
        
        // 测试2023年2月（平年）
        Date feb28_2023 = DateUtil.parse("2023-02-28", "yyyy-MM-dd");
        Date endOfFeb2023 = DateUtil.endOfMonth(feb28_2023);
        
        assertTrue(DateUtil.isSameDay(feb28_2023, endOfFeb2023), "2023年2月28日应该是月末");
    }
}
