package org.jeecg.modules.cw.ckc.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采矿场综合成本表行数据
 */
@Data
public class CwCkcRow {
    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 单位
     */
    private String unit;

    /**
     * 平均总耗
     */
    private String pjzh;

    /**
     * 平均单价
     */
    private String pjdj;

    /**
     * 月预算
     */
    private String yys;

    /**
     * 月累计
     */
    private String ylj;
    
    /**
     * 月预测（下半月预测数据）
     */
    private String yyc;

    /**
     * 月末实际数据
     */
    private String ymsj;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当日数
     */
    private String drs;
}
