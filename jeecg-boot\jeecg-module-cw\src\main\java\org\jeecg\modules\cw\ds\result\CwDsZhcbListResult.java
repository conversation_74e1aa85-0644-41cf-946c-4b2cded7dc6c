package org.jeecg.modules.cw.ds.result;

import lombok.Data;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.ds.entity.CwDsRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwDsZhcbListResult {
    private Date queryDate;
    private BigDecimal dsCll;
    private BigDecimal cllyc;
    private List<CwDsRow> rows;

    /**
     * 是否存在下半月预测数据
     */
    private Boolean hasSecondHalfForecast;
}
