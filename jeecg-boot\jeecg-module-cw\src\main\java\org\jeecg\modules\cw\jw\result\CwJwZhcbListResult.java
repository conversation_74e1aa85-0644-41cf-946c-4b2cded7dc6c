package org.jeecg.modules.cw.jw.result;

import lombok.Data;
import org.jeecg.modules.cw.jw.entity.CwJwRow;
import org.jeecg.modules.cw.sx.entity.CwSxRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CwJwZhcbListResult {
    private Date queryDate;
    private BigDecimal jwCll;
    private List<CwJwRow> rows;

    // 字段
    private BigDecimal cllyc; // 处理量预测
    private Boolean isFirstHalf; // 是否上半月

    /**
     * 是否存在下半月预测数据
     */
    private Boolean hasSecondHalfForecast;
}
