package org.jeecg.modules.cw.sx.result;

import lombok.Data;
import org.jeecg.modules.cw.ckc.entity.CwCkcRow;
import org.jeecg.modules.cw.sx.entity.CwSxRow;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 泗选厂综合成本表查询结果
 */
@Data
public class CwSxZhcbListResult {
    /**
     * 查询日期
     */
    private Date queryDate;

    /**
     * 泗选厂处理量
     */
    private BigDecimal sxCll;

    /**
     * 行数据
     */
    private List<CwSxRow> rows;

    /**
     * 处理量预测
     */
    private BigDecimal cllyc;

    /**
     * 是否上半月
     */
    private Boolean isFirstHalf;

    /**
     * 总月预测
     */
    private BigDecimal yyc;

    /**
     * 是否存在下半月预测数据
     */
    private Boolean hasSecondHalfForecast;
}
